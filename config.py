# Centralized Game Configuration
# This file contains commonly accessed game variables and settings for easier maintenance

from enum import Enum, auto
import pygame

# ============================================================================
# DISPLAY AND WINDOW SETTINGS
# ============================================================================

# Window settings
TITLE = "Simple Roguelike"
SCREEN_WIDTH = 1880
SCREEN_HEIGHT = 920
FPS = 60
DEFAULT_WINDOWED_SIZE = (SCREEN_WIDTH, SCREEN_HEIGHT)
FULLSCREEN_ENABLED = True

# ============================================================================
# CAMERA AND ZOOM SETTINGS
# ============================================================================

# Zoom configuration
DEFAULT_ZOOM_LEVEL = 1.0
MIN_ZOOM_LEVEL = 0.5
MAX_ZOOM_LEVEL = 3.0
ZOOM_SENSITIVITY = 0.1  # How much zoom changes per scroll wheel tick

# ============================================================================
# PLAYER SETTINGS
# ============================================================================

# Player base stats
PLAYER_HEALTH = 100
PLAYER_BASE_SPEED = 2.0  # Significantly increased from 5 for faster gameplay
PLAYER_DAMAGE = 10
PLAYER_FIRE_RATE = 500  # milliseconds between shots

# Player progression
UPGRADE_POINTS_PER_LEVEL = 1
MAX_HEALTH_UPGRADE = 20
MAX_DAMAGE_UPGRADE = 5
MAX_SPEED_UPGRADE = 0.3  # Reduced from 0.5 to prevent game becoming too difficult
FIRE_RATE_UPGRADE = 50

# ============================================================================
# ENEMY SETTINGS
# ============================================================================

# Enemy base stats
ENEMY_HEALTH = 30
ENEMY_BASE_SPEED = 1.0  # Significantly increased from 3 for more challenging gameplay
ENEMY_DAMAGE = 5
ENEMY_FIRE_RATE = 1000  # milliseconds between shots

# Enemy type speed multipliers
ENEMY_SPEED_MULTIPLIERS = {
    "normal": 1.0,
    "fast": 1.5,
    "tank": 0.7,
    "berserker": 1.3,
    "boss": 0.8
}

# Enemy scaling - Increased density significantly
MAX_ENEMIES_BASE = 50  # Increased from 10
ENEMY_SCALING_FACTOR = 4  # Increased from 2
MAX_ENEMIES_CAP = 120  # Increased from 30

# Boss settings
BOSS_HEALTH_MULTIPLIER = 3.0
BOSS_DAMAGE_MULTIPLIER = 1.5
BOSS_SIZE_MULTIPLIER = 2.0
BOSS_SPAWN_LEVEL = 5

# ============================================================================
# LEVEL GENERATION SETTINGS
# ============================================================================

# Level dimensions - Base sizes that scale with level
LEVEL_WIDTH = 60  # in tiles - base width
LEVEL_HEIGHT = 45  # in tiles - base height
TILE_SIZE = 32

# Dynamic terrain generation based on camera
DYNAMIC_TERRAIN_ENABLED = True
TERRAIN_GENERATION_RADIUS = 40  # tiles around camera to generate
TERRAIN_CHUNK_SIZE = 16  # tiles per chunk for efficient generation
TERRAIN_CACHE_SIZE = 100  # maximum cached chunks

# Enhanced Terrain System
TERRAIN_VARIETY_ENABLED = True
TERRAIN_BIOME_SYSTEM_ENABLED = True
TERRAIN_DECORATION_DENSITY = 0.3  # 0.0 to 1.0 - density of decorative elements
TERRAIN_ENVIRONMENTAL_HAZARDS_ENABLED = True
TERRAIN_DESTRUCTIBLE_ELEMENTS_ENABLED = True

# Expanded Terrain Types and Textures
TERRAIN_TYPES = {
    # Basic terrain
    'floor': 0,
    'wall': 1,

    # Natural terrain
    'grass': 2,
    'grass_tall': 3,
    'grass_dry': 4,
    'dirt': 5,
    'dirt_rich': 6,
    'dirt_rocky': 7,

    # Stone and mineral
    'stone': 8,
    'stone_rough': 9,
    'stone_smooth': 10,
    'gravel': 11,
    'sand': 12,
    'sand_coarse': 13,

    # Water features
    'water': 14,
    'water_shallow': 15,
    'water_deep': 16,
    'ice': 17,

    # Wood and organic
    'wood': 18,
    'wood_old': 19,
    'wood_rotten': 20,
    'moss': 21,
    'mushroom': 22,

    # Special terrain
    'lava': 23,
    'crystal': 24,
    'metal': 25,
    'bone': 26,
    'ash': 27
}

# Enhanced Biome Configuration with multiple variants
BIOME_TYPES = {
    'dungeon': {
        'primary': 'floor',
        'secondary': 'stone',
        'decoration': 'gravel',
        'hazards': ['spike_trap', 'poison_gas'],
        'special_features': ['secret_door', 'treasure_alcove'],
        'lighting': 'dim',
        'spawn_weight': 1.0
    },
    'forest': {
        'primary': 'grass',
        'secondary': 'dirt',
        'decoration': 'wood',
        'hazards': ['thorn_bush', 'quicksand'],
        'special_features': ['hidden_grove', 'ancient_tree'],
        'lighting': 'natural',
        'spawn_weight': 1.0
    },
    'cave': {
        'primary': 'stone',
        'secondary': 'gravel',
        'decoration': 'water',
        'hazards': ['falling_rocks', 'underground_river'],
        'special_features': ['crystal_formation', 'underground_lake'],
        'lighting': 'dark',
        'spawn_weight': 1.0
    },
    'ruins': {
        'primary': 'stone_smooth',
        'secondary': 'sand',
        'decoration': 'wood_old',
        'hazards': ['crumbling_floor', 'ancient_trap'],
        'special_features': ['runic_circle', 'collapsed_tower'],
        'lighting': 'mystical',
        'spawn_weight': 0.8
    },
    'swamp': {
        'primary': 'dirt_rich',
        'secondary': 'water_shallow',
        'decoration': 'moss',
        'hazards': ['poisonous_bog', 'unstable_ground'],
        'special_features': ['witch_hut', 'dead_tree'],
        'lighting': 'eerie',
        'spawn_weight': 0.7
    },
    'volcanic': {
        'primary': 'ash',
        'secondary': 'stone_rough',
        'decoration': 'lava',
        'hazards': ['lava_pool', 'toxic_fumes'],
        'special_features': ['obsidian_formation', 'fire_geyser'],
        'lighting': 'fiery',
        'spawn_weight': 0.5
    },
    'crystal_cavern': {
        'primary': 'crystal',
        'secondary': 'stone',
        'decoration': 'ice',
        'hazards': ['crystal_shard', 'energy_discharge'],
        'special_features': ['power_crystal', 'teleport_gate'],
        'lighting': 'magical',
        'spawn_weight': 0.3
    },
    'necropolis': {
        'primary': 'bone',
        'secondary': 'ash',
        'decoration': 'metal',
        'hazards': ['cursed_ground', 'soul_drain'],
        'special_features': ['ancient_tomb', 'bone_throne'],
        'lighting': 'cursed',
        'spawn_weight': 0.4
    }
}

# Terrain Generation Parameters
TERRAIN_NOISE_SCALE = 0.1  # Scale for procedural noise
TERRAIN_SMOOTHING_PASSES = 2  # Number of smoothing iterations
TERRAIN_FEATURE_DENSITY = 0.15  # Density of special terrain features
TERRAIN_HAZARD_DENSITY = 0.05  # Density of environmental hazards
TERRAIN_SECRET_DENSITY = 0.02  # Density of secret areas

# Room generation with enhanced variety
ROOM_MAX_SIZE = 12
ROOM_MIN_SIZE = 6
MAX_ROOMS = 35
ROOM_TYPES = ['standard', 'large', 'corridor', 'circular', 'irregular', 'treasure', 'boss', 'puzzle']
ROOM_TYPE_WEIGHTS = [0.4, 0.2, 0.15, 0.1, 0.08, 0.04, 0.02, 0.01]  # Probability weights

# Progressive level scaling with enhanced mechanics
LEVEL_SIZE_SCALING_FACTOR = 1.8  # How much larger each level gets
MAX_LEVEL_SIZE_MULTIPLIER = 8.0  # Maximum size increase
ROOM_DENSITY_PER_LEVEL = 0.1  # Additional room density per level
COMPLEXITY_SCALING_FACTOR = 0.2  # How much more complex each level becomes
HAZARD_SCALING_FACTOR = 0.1  # How many more hazards per level

# ============================================================================
# ITEM AND LOOT SETTINGS
# ============================================================================

# Item spawn rates
ITEMS_PER_LEVEL = 5
EQUIPMENT_DROP_CHANCE = 0.15

# Item effects
HEALTH_POTION_HEAL = 25
DAMAGE_BOOST_AMOUNT = 5
SPEED_BOOST_AMOUNT = 1
FIRE_RATE_BOOST_AMOUNT = 50

# Special item effects
SHIELD_DURATION = 300  # frames
SHIELD_ABSORPTION = 50
XP_BOOST_AMOUNT = 25
MULTI_SHOT_DURATION = 600  # frames
INVINCIBILITY_DURATION = 180  # frames

# ============================================================================
# EXPERIENCE AND PROGRESSION SETTINGS
# ============================================================================

# XP rewards
XP_PER_ENEMY_BASE = 10
XP_PER_ENEMY_FAST = 8
XP_PER_ENEMY_TANK = 15
XP_PER_BOSS = 50
XP_PER_LEVEL = 50
XP_DIFFICULTY_MULTIPLIER = 1.2

# Skill system
SKILL_POINTS_PER_LEVEL = 1
MAX_SKILL_LEVEL = 5

# ============================================================================
# SCORING SETTINGS
# ============================================================================

ENEMY_KILL_SCORE = 100
ITEM_COLLECT_SCORE = 50
LEVEL_COMPLETE_SCORE = 500

# ============================================================================
# UI AND HUD SETTINGS
# ============================================================================

# HUD transparency
HUD_BACKGROUND_ALPHA = 100  # Reduced from 180 for better transparency

# Progress bar settings
HEALTH_BAR_COLOR = (0, 255, 0)  # Green
XP_BAR_COLOR = (0, 255, 255)    # Cyan

# Pause overlay
PAUSE_OVERLAY_ALPHA = 128
PAUSE_TITLE_FONT_SIZE = 72
PAUSE_INSTRUCTION_FONT_SIZE = 36

# ============================================================================
# KEYBOARD SHORTCUTS
# ============================================================================

# UI shortcuts
KEY_INVENTORY = pygame.K_i  # Equipment/Inventory screen
KEY_SKILLS = pygame.K_k     # Skills screen
KEY_ACHIEVEMENTS = pygame.K_o  # Achievements screen
KEY_CHARACTER_STATS = pygame.K_c  # Character stats screen
KEY_UPGRADE_MENU = pygame.K_u  # Upgrade menu (existing)
KEY_PAUSE = pygame.K_p      # Pause game (existing)

# Movement keys (for reference)
MOVEMENT_KEYS = {
    'up': [pygame.K_w, pygame.K_UP],
    'down': [pygame.K_s, pygame.K_DOWN],
    'left': [pygame.K_a, pygame.K_LEFT],
    'right': [pygame.K_d, pygame.K_RIGHT]
}

# ============================================================================
# PERFORMANCE SETTINGS
# ============================================================================

# Memory management
MAX_XP_MESSAGES = 50
SPRITE_CACHE_PADDING = 100
SPRITE_CULLING_BUFFER = 64
VISIBLE_SPRITE_CACHE_ENABLED = True

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED = True
PERFORMANCE_LOG_INTERVAL = 300  # frames between performance logs
FPS_TARGET = 60  # Target FPS for performance optimization
FPS_WARNING_THRESHOLD = 45  # Log warning if FPS drops below this
MEMORY_MONITORING_ENABLED = True
MEMORY_WARNING_THRESHOLD = 500  # MB - warn if memory usage exceeds this

# Adaptive Performance System
ADAPTIVE_PERFORMANCE_ENABLED = True
AUTO_REDUCE_ENEMIES_ON_LAG = True
AUTO_REDUCE_PARTICLES_ON_LAG = True
AUTO_REDUCE_EFFECTS_ON_LAG = True
PERFORMANCE_ADJUSTMENT_THRESHOLD = 40  # FPS threshold for automatic adjustments

# Rendering optimization
MAX_VISIBLE_ENEMIES = 50  # Limit enemies rendered at once
PARTICLE_SYSTEM_ENABLED = True
MAX_PARTICLES = 200
ANIMATION_FRAME_SKIP = 0  # Skip frames for performance (0 = no skip)

# Enemy Performance Optimization
ENEMY_AI_OPTIMIZATION_ENABLED = True
ENEMY_AI_UPDATE_DISTANCE = 800  # pixels - enemies beyond this distance use simplified AI
ENEMY_AI_SLEEP_DISTANCE = 1200  # pixels - enemies beyond this distance sleep
ENEMY_AI_UPDATE_FREQUENCY_DISTANT = 10  # frames between updates for distant enemies
ENEMY_AI_UPDATE_FREQUENCY_CLOSE = 1  # frames between updates for close enemies
ENEMY_LOD_SYSTEM_ENABLED = True  # Level of Detail system for enemies
ENEMY_CULLING_ENABLED = True  # Completely disable enemies very far away
ENEMY_CULLING_DISTANCE = 2000  # pixels - enemies beyond this are completely disabled
MAX_ACTIVE_ENEMIES = 100  # Maximum number of enemies that can be active at once

# Camera performance
CAMERA_SMOOTH_FOLLOW = True
CAMERA_FOLLOW_SPEED = 0.1  # 0.1 = smooth, 1.0 = instant

# ============================================================================
# AUDIO SETTINGS
# ============================================================================

# Volume levels
MASTER_VOLUME = 0.7
SFX_VOLUME = 0.8
MUSIC_VOLUME = 0.6

# ============================================================================
# GAME MECHANICS SETTINGS
# ============================================================================

# Projectile settings
PROJECTILE_SPEED = 10

# Pathfinding and AI behavior
PATHFINDING_MAX_DISTANCE = 200
RANDOM_MOVE_CHANCE = 0.02
ENEMY_DETECTION_RADIUS = 300  # pixels
ENEMY_ATTACK_RANGE = 150  # pixels
ENEMY_GROUP_COORDINATION_RANGE = 100  # pixels for group behavior
ENEMY_RETREAT_HEALTH_THRESHOLD = 0.2  # retreat when health below 20%
ENEMY_AGGRESSION_SCALING = 0.1  # aggression increase per level
ENEMY_FORMATION_SPACING = 40  # pixels between enemies in formation
ENEMY_PATROL_RADIUS = 80  # pixels for patrol behavior
ENEMY_IDLE_DURATION_MIN = 1000  # milliseconds
ENEMY_IDLE_DURATION_MAX = 3000  # milliseconds

# Equipment
EQUIPMENT_UPGRADE_COST_BASE = 100
EQUIPMENT_SET_BONUS_THRESHOLD = 2

# ============================================================================
# LEVEL COMPLETION SETTINGS
# ============================================================================

# Stair system for level progression
STAIRS_ENABLED = True
REQUIRE_ENEMY_PERCENTAGE_FOR_STAIRS = True  # Require defeating % of enemies to unlock stairs
ENEMY_DEFEAT_PERCENTAGE_FOR_STAIRS = 0.5  # 50% of enemies must be defeated
STAIRS_UNLOCK_MESSAGE_DURATION = 180  # 3 seconds at 60 FPS
MULTIPLE_EXITS_ENABLED = True  # Allow multiple exit options per level
MAX_EXITS_PER_LEVEL = 3  # Maximum number of exits
EXIT_TYPES = ['stairs_down', 'portal', 'secret_passage', 'teleporter']

# Camera system improvements
CAMERA_FOLLOW_BEYOND_BOUNDARIES = True  # Allow camera to follow player beyond map edges
CAMERA_BOUNDARY_BUFFER = 100  # Pixels of buffer beyond map edges

# Achievement system
ACHIEVEMENT_XP_BONUS = 25

# ============================================================================
# ENVIRONMENTAL HAZARDS AND FEATURES
# ============================================================================

# Environmental Hazards Configuration
ENVIRONMENTAL_HAZARDS = {
    'spike_trap': {
        'damage': 15,
        'trigger_chance': 0.8,
        'visual_warning': True,
        'cooldown': 2000  # ms
    },
    'poison_gas': {
        'damage': 5,
        'duration': 3000,  # ms
        'spread_radius': 64,
        'visual_effect': 'green_cloud'
    },
    'thorn_bush': {
        'damage': 8,
        'slow_effect': 0.5,
        'duration': 1500,
        'visual_effect': 'thorns'
    },
    'quicksand': {
        'damage': 0,
        'slow_effect': 0.2,
        'escape_difficulty': 0.3,
        'visual_effect': 'bubbling'
    },
    'falling_rocks': {
        'damage': 25,
        'warning_time': 1000,  # ms
        'area_size': 96,
        'visual_warning': True
    },
    'lava_pool': {
        'damage': 30,
        'damage_interval': 500,  # ms
        'visual_effect': 'fire_particles'
    },
    'crystal_shard': {
        'damage': 20,
        'knockback': 50,
        'visual_effect': 'crystal_explosion'
    },
    'cursed_ground': {
        'damage': 10,
        'mana_drain': 5,
        'visual_effect': 'dark_aura'
    }
}

# Special Features Configuration
SPECIAL_FEATURES = {
    'secret_door': {
        'discovery_chance': 0.3,
        'requires_key': False,
        'reward_type': 'treasure'
    },
    'treasure_alcove': {
        'loot_multiplier': 2.0,
        'rare_item_chance': 0.4,
        'visual_effect': 'golden_glow'
    },
    'hidden_grove': {
        'healing_rate': 2,  # HP per second
        'mana_regen': 1,
        'duration': 10000  # ms
    },
    'crystal_formation': {
        'mana_boost': 50,
        'temporary_power': True,
        'duration': 30000  # ms
    },
    'runic_circle': {
        'teleport_destination': 'random',
        'activation_cost': 10,  # mana
        'visual_effect': 'magic_circle'
    },
    'power_crystal': {
        'damage_boost': 1.5,
        'duration': 20000,  # ms
        'visual_effect': 'energy_aura'
    },
    'ancient_tomb': {
        'undead_spawn_chance': 0.6,
        'treasure_quality': 'epic',
        'curse_chance': 0.2
    }
}

# Weather and Environmental Effects
WEATHER_SYSTEM_ENABLED = True
WEATHER_TYPES = {
    'clear': {'visibility': 1.0, 'movement_modifier': 1.0, 'spawn_rate': 0.4},
    'rain': {'visibility': 0.8, 'movement_modifier': 0.9, 'spawn_rate': 0.2},
    'fog': {'visibility': 0.6, 'movement_modifier': 1.0, 'spawn_rate': 0.15},
    'storm': {'visibility': 0.7, 'movement_modifier': 0.8, 'spawn_rate': 0.1},
    'blizzard': {'visibility': 0.5, 'movement_modifier': 0.7, 'spawn_rate': 0.05},
    'sandstorm': {'visibility': 0.4, 'movement_modifier': 0.6, 'spawn_rate': 0.05},
    'volcanic_ash': {'visibility': 0.3, 'movement_modifier': 0.8, 'spawn_rate': 0.05}
}
WEATHER_CHANGE_INTERVAL = 120000  # ms - 2 minutes
WEATHER_TRANSITION_TIME = 5000  # ms

# ============================================================================
# ADVANCED GAME BALANCE
# ============================================================================

# Difficulty scaling
DIFFICULTY_SCALING_ENABLED = True
DIFFICULTY_INCREASE_PER_LEVEL = 0.15  # 15% increase per level
MAX_DIFFICULTY_MULTIPLIER = 5.0  # Cap difficulty scaling

# Combat balance
CRITICAL_HIT_CHANCE = 0.05  # 5% base critical hit chance
CRITICAL_HIT_MULTIPLIER = 2.0  # 2x damage on critical hits
DODGE_CHANCE_BASE = 0.02  # 2% base dodge chance
BLOCK_CHANCE_BASE = 0.03  # 3% base block chance

# Loot and rewards scaling
LOOT_QUALITY_SCALING = 0.1  # Better loot quality per level
RARE_ITEM_CHANCE_BASE = 0.05  # 5% base chance for rare items
EQUIPMENT_DURABILITY_ENABLED = False  # Equipment degradation system

# Visual feedback settings
DAMAGE_NUMBER_DISPLAY = True
DAMAGE_NUMBER_DURATION = 60  # frames
SCREEN_SHAKE_ENABLED = True
SCREEN_SHAKE_INTENSITY = 2  # pixels
FLASH_EFFECT_ENABLED = True

# Minimap settings
MINIMAP_ENABLED = True
MINIMAP_SIZE = 150  # pixels
MINIMAP_TRANSPARENCY = 180  # alpha value
MINIMAP_UPDATE_FREQUENCY = 5  # frames between updates

# ============================================================================
# GAME STATES
# ============================================================================

class GameState(Enum):
    START = auto()
    PLAYING = auto()
    GAME_OVER = auto()
    UPGRADE = auto()
    PAUSE = auto()

# ============================================================================
# COLORS
# ============================================================================

# Basic colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
BROWN = (165, 42, 42)
GRAY = (128, 128, 128)
ORANGE = (255, 165, 0)
CYAN = (0, 255, 255)

# Equipment rarity colors
EQUIPMENT_RARITY_COLORS = {
    "Common": (200, 200, 200),      # Light Gray
    "Uncommon": (0, 255, 0),        # Green
    "Rare": (0, 100, 255),          # Blue
    "Epic": (128, 0, 128)           # Purple
}
